"""
四层架构多Agent工作流框架

架构设计：
┌─────────────────────────────────────────────────────────────┐
│                    用户输入                                   │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│              意图理解与复杂度分析层                             │
│  • 任务复杂度评估    • 所需能力识别    • 执行模式选择              │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                工作级规划层 (Work Level)                      │
│  • 目标分解        • 策略制定        • 资源需求分析              │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                任务级规划层 (Task Level)                      │
│  • 具体任务生成    • 工具选择与匹配    • 依赖关系构建              │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                  执行引擎层                                   │
│  • 并行任务执行    • 迭代优化    • 结果合并                      │
└─────────────────────────────────────────────────────────────┘

核心特性：
- 智能复杂度分析：自动识别简单任务vs复杂任务
- 分层规划：简单任务快速处理，复杂任务深度规划
- 工具生态：可扩展的工具注册和匹配机制
- 执行优化：支持并行执行和依赖关系管理
"""
import json
import operator
from typing import Annotated, Optional, Dict, Any, List

from langchain_core.messages import AnyMessage, AIMessage
from langchain_core.output_parsers import PydanticOutputParser
from langchain_core.prompts import ChatPromptTemplate
from langgraph.config import get_stream_writer
from langgraph.graph import StateGraph, START, END, add_messages
from langgraph.types import Send
from pydantic import BaseModel, Field
from typing_extensions import Literal
from typing_extensions import TypedDict

from sub_agents.rep.utils.llm_utils import get_a_llm
from sub_agents.sql_database_chain.violation_data_search_new.core.vector_search import execute_multi_query_rerank_search
from utils.load_env import logger


# 通用状态定义
class GenericWorkflowState(TypedDict):
    """人类化工作流状态"""
    messages: Annotated[list[AnyMessage], add_messages]
    context: Optional[Dict[str, Any]]  # 业务上下文

    # 核心字段
    intent_type: Optional[str]  # 意图类型
    user_goal: Optional[str]  # 用户真实目标
    approach: Optional[str]  # 解决思路
    need_chart: Optional[bool]  # 是否需要生成图表

    # 任务执行
    current_tasks: Optional[List[Dict[str, Any]]]  # 当前任务列表
    task_results: Annotated[List[Dict[str, Any]], operator.add]  # 任务结果 - 使用官方推荐的operator.add
    iteration_count: Optional[int]  # 迭代次数
    max_iterations: Optional[int]  # 最大迭代次数

    # 迭代控制
    need_more_tasks: Optional[bool]  # 是否需要更多任务
    new_iteration_tasks: Optional[List[Dict[str, Any]]]  # 新的迭代任务

    final_result: Optional[str]  # 最终结果


# 单个任务状态
class TaskState(TypedDict):
    """单个任务状态 - 参考官方 WorkerState 模式"""
    task: Dict[str, Any]  # 任务信息
    context: Optional[Dict[str, Any]]  # 业务上下文
    task_results: Annotated[List[Dict[str, Any]], operator.add]  # 使用官方推荐的operator.add


# Pydantic模型
class Task(BaseModel):
    """人类化任务模型"""
    id: str = Field(description="任务ID")
    description: str = Field(description="任务描述")
    query: str = Field(description="查询内容")
    tool_type: str = Field(description="工具类型")
    intent: str = Field(description="任务意图：为了什么目的做这个任务")
    status: str = Field(default="not_started", description="任务状态：not_started/in_progress/completed")
    dependencies: List[str] = Field(default=[], description="依赖的任务ID列表")
    priority: int = Field(default=1, description="任务优先级，数字越小优先级越高")


class WorkLevelPlanningResult(BaseModel):
    """工作级规划结果"""
    reasoning: str = Field(description="规划思考过程，说明目标分解和策略制定的依据")
    user_goal: str = Field(description="用户的最终目标")
    sub_goals: List[str] = Field(description="分解的子目标列表")
    strategy: str = Field(description="总体解决策略")
    required_capabilities: List[str] = Field(description="所需能力列表")
    work_phases: List[Dict[str, str]] = Field(description="工作阶段列表，包含phase、objective、deliverable")


class PlanningResult(BaseModel):
    """任务级规划结果"""
    reasoning: str = Field(description="任务规划思考过程，说明任务拆解的依据与目标")
    user_goal: str = Field(description="用户的真实目标")
    approach: str = Field(description="解决思路")
    initial_tasks: List[Task] = Field(description="初始任务列表")
    need_chart: bool = Field(description="是否需要生成图表")
    execution_strategy: str = Field(default="sequential", description="执行策略：sequential/parallel")


class IntentClassificationResult(BaseModel):
    """意图分类结果"""
    intent_type: str = Field(description="意图类型")
    confidence: float = Field(description="置信度")
    response: Optional[str] = Field(description="直接回复内容（如果适用）")


class IterationDecision(BaseModel):
    """迭代决策结果"""
    reasoning: str = Field(description="决策思考过程，说明是否需要更多任务的分析依据")
    need_more_tasks: bool = Field(description="是否需要更多任务")
    new_tasks: List[Task] = Field(description="新增任务列表")


# 简单对话回复生成器
class SimpleResponseGenerator:
    """专门处理简单对话的回复生成器"""

    def __init__(self, llm):
        self.llm = llm

        self.prompt = ChatPromptTemplate.from_messages([
            ("system", """你是一个专业、友好的违规案例查询助手。用户刚才发送了简单的对话内容，请生成一个合适的回复。

你的特点：
- 专业但亲切，不过分正式
- 主动介绍你的能力（数据查询、分析等）
- 引导用户提出具体问题
- 回复简洁明了，不要太长

根据用户输入类型生成回复：
- 问候语：友好回应并介绍能力
- 感谢语：谦逊回应并提供进一步帮助
- 测试输入：确认系统正常并介绍功能
- 闲聊内容：礼貌回应并引导到专业话题
- 其他：提供友好的默认回复

请直接返回回复内容，不要添加额外格式。"""),
            ("user", "{user_input}")
        ])

    async def generate_response(self, user_input: str) -> str:
        """生成简单对话回复"""
        try:
            chain = self.prompt | self.llm
            response = await chain.ainvoke({"user_input": user_input})
            return response.content
        except Exception as e:
            logger.warning(f"简单回复生成失败，使用默认回复: {e}")
            # 降级处理
            return "您好！我是智能助手，可以帮您查询和分析各种信息。请问有什么可以帮助您的吗？"


class ChartGenerator:
    """图表可视化数据生成器"""

    def __init__(self, llm):
        self.llm = llm

        self.prompt = ChatPromptTemplate.from_messages([
            ("system", """你是专业的违规案例总结智能助手，负责根据当前的查询结果和数据，选取合适的echarts图表类型，生成对应结构的json数据。

你需要：
1. **深度分析、理解违规案例内容**：{final_results}
2. **选择合适的chart图表类型**
3. **确保生成的json对象格式符合echarts.js库，确保生成的图表内容为中文**

## 输出格式

**直接输出json对象，不需要回答额外的内容**，如果分析出多个json对象，直接将它们以下面格式输出：

```json 
 对象1 
```
```json 
 对象2 
```

## 图表类型

### 折线图/柱状图/饼图等

根据总结内容和查询结果，以给定数据结构构建一个或多个能够被echarts库直接渲染的json对象，图表类型由你来决定。

基本的echarts图表结构示例如下：

{{
    id: '10001',
    name: 'xx情况折线图',
    type: 'xxx',
    option: {{
        legend: {{}},
        tooltip: {{}},
        dataset: {{  // 必须使用dataset
            source: [
              ['product', '2015', '2016', '2017'],
              ['Matcha Latte', 43.3, 85.8, 93.7],
              ['Milk Tea', 83.1, 73.4, 55.1],
              ['Cheese Cocoa', 86.4, 65.2, 82.5],
              ['Walnut Brownie', 72.4, 53.9, 39.1]
           ]
        }},
        xAxis: {{ type: 'category' }},
        yAxis: {{}},
        series: [{{ type: 'line' }}, {{ type: 'bar' }}, {{ type: 'pie' }}]
    }};
}}

## 其他注意事项

- 不要生成 type 为 table 的图表
- 必须严格使用数据集dataset这个属性

"""),
            ("user", "{final_results}")
        ])

    async def generate_response(self, task_results: list, messages: list) -> str:
        """生成可视化图表回复"""
        try:
            # 合并任务结果为字符串
            final_results = "\n".join([str(result) for result in task_results])
            chain = self.prompt | self.llm
            response = await chain.ainvoke({
                "final_results": final_results,
                "messages": messages
            })
            return response.content
        except Exception as e:
            logger.warning(f"生成可视化图表数据失败，使用默认回复: {e}")
            # 降级处理
            return "您好！我是智能助手，可以帮您查询和分析各种信息。请问有什么可以帮助您的吗？"


# 第一层：意图理解与复杂度分析层
class EnhancedSupervisorAgent:
    """第一层：意图理解与复杂度分析层

    职责：
    • 任务复杂度评估 - 判断simple_chat/simple_task/complex_task
    • 所需能力识别 - 分析需要哪些工具能力
    • 执行模式选择 - 决定使用简单规划还是复杂规划
    """

    def __init__(self, llm, tool_registry):
        self.llm = llm
        self.tool_registry = tool_registry
        self.parser = PydanticOutputParser(pydantic_object=IntentClassificationResult)

        self.prompt = ChatPromptTemplate.from_messages([
            ("system", """你是智能助手的监督者，负责意图识别和复杂度评估。

可用工具：
{available_tools}

判断用户输入类型：
1. **simple_chat**: 问候、感谢、闲聊等
2. **simple_task**: 简单查询，1-2步就能完成
3. **complex_task**: 复杂任务，需要多步骤、多工具协作

复杂度判断标准：
- simple_task: 单一查询、简单问题
- complex_task: 包含"分析"、"对比"、"生成报告"、"详细"等关键词，或描述超过30字

{format_instructions}

注意：response字段始终为null。

示例：
用户："你好"
输出：{{"intent_type": "simple_chat", "confidence": 0.95, "response": null}}

用户："查询用户数据"
输出：{{"intent_type": "simple_task", "confidence": 0.98, "response": null}}

用户："分析过去三年的销售数据并生成详细报告"
输出：{{"intent_type": "complex_task", "confidence": 0.95, "response": null}}"""),
            ("placeholder", "{messages}"),
        ])

    async def analyze_intent_and_complexity(self, messages: List[AnyMessage]) -> Dict[str, Any]:
        """意图识别和复杂度分析"""
        available_tools = self.tool_registry.get_available_tools_desc()

        try:
            chain = self.prompt | self.llm | self.parser
            result = await chain.ainvoke({
                "messages": messages,
                "available_tools": available_tools,
                "format_instructions": self.parser.get_format_instructions()
            })
            logger.info(f"[EnhancedSupervisorAgent] 意图识别完成: {result.model_dump()}")
            return result.model_dump()
        except Exception as e:
            # 降级处理：默认为简单任务
            logger.info(f"[WARNING] 意图识别失败，使用默认策略: {e}")
            return {
                "intent_type": "simple_task",
                "confidence": 0.5,
                "response": None
            }

    # 保持接口兼容
    async def classify_intent(self, messages: List[AnyMessage]) -> Dict[str, Any]:
        """分类用户意图（兼容接口）"""
        return await self.analyze_intent_and_complexity(messages)


# 第二层：工作级规划层 (Work Level)
class WorkLevelPlanner:
    """工作级规划层 - 目标分解、策略制定、资源需求分析"""

    def __init__(self, llm):
        self.llm = llm
        self.parser = PydanticOutputParser(pydantic_object=WorkLevelPlanningResult)

        self.prompt = ChatPromptTemplate.from_messages([
            ("system", """你是工作级规划师，负责将用户需求转化为宏观工作策略。

# 角色定义
你是一个智能助手，名叫Genie，专门负责工作级规划。

# 工作流程
## 先思考 (Reasoning)
- 逐步思考：深度推理分析用户输入，识别核心需求及潜在挑战
- 目标分析：用户的最终目标是什么？背后的真实需求是什么？
- 策略思考：采用什么总体方法来实现这些目标？
- 资源评估：需要什么类型的信息、工具和能力？
- 建议控制"思考过程 Reasoning"内容在 200 字以内

## 然后规划 (Planning)
你的职责：
1. **目标分解** - 将复杂问题分解为可管理、可执行、独立且清晰的子目标
2. **策略制定** - 制定数据驱动的总体解决策略
3. **资源需求分析** - 识别所需的核心能力和工具类型
4. **阶段规划** - 按逻辑顺序组织工作阶段，确保上下阶段逻辑连贯

## 规划原则
- 子目标数量：最多不超过5个子目标
- 独立性要求：子目标之间不重复、不交叠
- 逻辑连贯性：按顺序或因果逻辑组织
- 完整性检查：每个阶段都有明确的目标和交付物

{format_instructions}

示例：
用户："分析过去三年的违规案例趋势并生成详细报告"
输出：
{{
    "reasoning": "用户需要了解违规案例的发展趋势，这是一个复杂的分析任务。需要先收集历史数据，然后进行趋势分析，最后生成可视化报告。整个过程需要数据查询、统计分析和报告生成能力。",
    "user_goal": "了解违规案例发展趋势，为决策提供数据支持",
    "sub_goals": ["获取历史数据", "识别趋势模式", "生成分析报告"],
    "strategy": "数据驱动的趋势分析方法",
    "required_capabilities": ["数据查询", "趋势分析", "报告生成"],
    "work_phases": [
        {{"phase": "数据收集", "objective": "获取完整的历史违规数据", "deliverable": "结构化数据集"}},
        {{"phase": "趋势分析", "objective": "识别时间和类型趋势", "deliverable": "趋势分析结果"}},
        {{"phase": "报告生成", "objective": "形成可视化分析报告", "deliverable": "详细分析报告"}}
    ]
}}"""),
            ("user", "{user_input}")
        ])

    async def plan(self, message: AnyMessage) -> Dict[str, Any]:
        """执行工作级规划"""
        try:
            chain = self.prompt | self.llm | self.parser
            result = await chain.ainvoke({
                "user_input": str(message.content),
                "format_instructions": self.parser.get_format_instructions()
            })
            logger.info(f"[WorkLevelPlanner] 工作级规划完成: {result.user_goal}")
            return result.model_dump()
        except Exception as e:
            logger.warning(f"工作级规划失败，使用默认规划: {e}")
            return {
                "reasoning": "由于规划失败，采用简单直接的处理方式",
                "user_goal": str(message.content),
                "sub_goals": [str(message.content)],
                "strategy": "直接处理用户请求",
                "required_capabilities": ["数据查询"],
                "work_phases": [
                    {"phase": "执行任务", "objective": "完成用户请求", "deliverable": "查询结果"}
                ]
            }


# 第三层：任务级规划层 (Task Level)
class TaskLevelPlanner:
    """任务级规划层 - 具体任务生成、工具选择与匹配、依赖关系构建"""

    def __init__(self, llm, tool_registry):
        self.llm = llm
        self.tool_registry = tool_registry
        self.parser = PydanticOutputParser(pydantic_object=PlanningResult)

    async def plan(self, work_plan: Dict, available_tools: str) -> Dict[str, Any]:
        """基于工作级规划生成具体任务"""

        prompt = ChatPromptTemplate.from_messages([
            ("system", """你是任务级规划师，负责将工作级规划转化为具体的可执行任务。

# 角色定义
你是一个智能助手，名叫Genie，专门负责任务级规划。

# 工作流程
## 先思考 (Reasoning)
- 逐步思考：基于工作级规划，逐步思考如何将宏观策略转化为具体任务
- 任务分解：为每个工作阶段生成具体、可执行的任务
- 工具匹配：为每个任务选择最合适的工具类型
- 依赖分析：识别任务间的依赖关系和执行顺序
- 建议控制"思考过程 Reasoning"内容在 200 字以内

## 然后规划 (Planning)
工作级规划输入：
- 最终目标：{user_goal}
- 子目标：{sub_goals}
- 总体策略：{strategy}
- 工作阶段：{work_phases}
- 所需能力：{required_capabilities}

可用工具：
{available_tools}

# 任务拆解详细规则

## 拆解任务约束
- **数量限制**：最多不超过5个任务，避免过度拆解
- **独立性要求**：任务之间不重复、不交叠，每个任务都是完整的子任务
- **逻辑连贯性**：任务按顺序或因果逻辑组织，上下任务逻辑连贯
- **完整性检查**：每一个子任务都是一个完整的子任务，例如读取文件后，将文件中的表格抽取出来形成表格保存

## 工具使用技巧
### 多维度查询策略
- 需要使用搜索工具时，每次至少执行2次搜索工具调用，每一个入参都是当前需要搜索的任务
- 例如："分析泡泡玛特股价分析"，可以从以下维度：'财务数据'，'公司战略'，'市场表现'，'投资者情绪'，'估值分析'，'行业趋势'，'竞争格局'等维度
- 从而可以形成如下搜索入参："泡泡玛特 财务数据 公司战略 行业趋势 市场表现"，"潮流玩具 竞争格局 行业发展趋势与规模"等完整搜索词

### 时间信息处理
- 对于时间信息需要特定理解和处理，特别是'最近三年'、'近三年'、'过去三年'、'去年'等
- 例如对于'最近3年'的原始输入"分析腾讯最近3年公开的财报"，可以对其中表示时间片段'最近3年'进行细化重新生成query："分析腾讯最近3年（2021，2022，2023）公开的财报"

## 任务设计原则
- **明确性**：每个任务描述清晰、具体，避免模糊表达
- **可执行性**：任务必须是可以通过现有工具完成的
- **目标导向**：每个任务都有明确的intent，说明为了什么目的做这个任务
- **优先级管理**：为任务设置合理的优先级和依赖关系

## 图表生成判断
- 当用户明确要求"图表"、"可视化"、"图形"时，设置need_chart为true
- 当任务涉及数据对比、趋势分析、统计汇总时，建议设置need_chart为true
- 简单查询或单一信息获取，设置need_chart为false

{format_instructions}

输出示例：
{{
    "reasoning": "基于工作级规划，需要将违规案例趋势分析分解为具体任务。首先收集历史数据，然后进行多维度分析，最后汇总趋势。考虑到需要对比分析，建议生成图表。",
    "user_goal": "了解违规案例发展趋势",
    "approach": "数据驱动的趋势分析方法",
    "initial_tasks": [
        {{
            "id": "task_1",
            "description": "收集2021-2024年违规案例数据",
            "query": "2021年到2024年违规案例数据 历史统计",
            "tool_type": "query_tool",
            "intent": "获取基础数据",
            "status": "not_started",
            "dependencies": [],
            "priority": 1
        }},
        {{
            "id": "task_2",
            "description": "分析违规案例类型和行业分布",
            "query": "违规案例类型统计 行业分布 处罚类型",
            "tool_type": "query_tool",
            "intent": "分类分析",
            "status": "not_started",
            "dependencies": ["task_1"],
            "priority": 2
        }}
    ],
    "need_chart": true,
    "execution_strategy": "sequential"
}}"""),
            ("user", "工作级规划：{work_plan}")
        ])

        try:
            chain = prompt | self.llm | self.parser
            result = await chain.ainvoke({
                "work_plan": work_plan,
                "user_goal": work_plan.get("user_goal"),
                "sub_goals": work_plan.get("sub_goals"),
                "strategy": work_plan.get("strategy"),
                "work_phases": work_plan.get("work_phases"),
                "required_capabilities": work_plan.get("required_capabilities"),
                "available_tools": available_tools,
                "format_instructions": self.parser.get_format_instructions()
            })
            logger.info(f"[TaskLevelPlanner] 任务级规划完成，生成{len(result.initial_tasks)}个任务")
            return result.model_dump()
        except Exception as e:
            logger.warning(f"任务级规划失败，使用默认规划: {e}")
            return self._get_default_task_plan(work_plan)

    def _get_default_task_plan(self, work_plan: Dict) -> Dict[str, Any]:
        """默认任务规划"""
        return {
            "reasoning": "由于任务规划失败，采用简单直接的处理方式",
            "user_goal": work_plan.get("user_goal", "完成用户请求"),
            "approach": work_plan.get("strategy", "直接处理"),
            "initial_tasks": [{
                "id": "task_1",
                "description": work_plan.get("user_goal", "执行任务"),
                "query": work_plan.get("user_goal", "查询"),
                "tool_type": "query_tool",
                "intent": "获取信息",
                "status": "not_started",
                "dependencies": [],
                "priority": 1
            }],
            "need_chart": False,
            "execution_strategy": "sequential"
        }


# 四层架构协调器
class FourLayerPlanningCoordinator:
    """四层架构规划协调器 - 协调意图理解、工作级规划、任务级规划"""

    def __init__(self, llm, tool_registry):
        self.llm = llm
        self.tool_registry = tool_registry
        # 第二层：工作级规划
        self.work_planner = WorkLevelPlanner(llm)
        # 第三层：任务级规划
        self.task_planner = TaskLevelPlanner(llm, tool_registry)

    async def plan(self, message: AnyMessage, intent_result: Dict) -> Dict[str, Any]:
        """执行四层架构规划"""
        intent_type = intent_result.get("intent_type", "simple_task")

        if intent_type == "simple_task":
            return await self._plan_simple_task(message)
        else:
            return await self._plan_complex_task(message)

    async def _plan_simple_task(self, message: AnyMessage) -> Dict[str, Any]:
        """简单任务规划 - 跳过工作级规划，直接生成任务"""
        available_tools = self.tool_registry.get_available_tools_desc()

        prompt = ChatPromptTemplate.from_messages([
            ("system", """你是简单任务规划师，为简单直接的任务快速生成执行计划。

# 角色定义
你是一个智能助手，名叫Genie，专门负责简单任务规划。

# 工作流程
## 先思考 (Reasoning)
- 快速分析：分析用户的简单需求，确定最直接的解决方案
- 任务设计：为简单任务设计1-2个具体的执行步骤
- 工具选择：选择最合适的工具来完成任务
- 建议控制"思考过程 Reasoning"内容在 100 字以内

## 然后规划 (Planning)
可用工具：
{available_tools}

对于简单任务，直接生成1-2个具体任务即可，遵循以下原则：
- 任务描述清晰具体
- 查询词优化，包含关键信息
- 设置合理的任务状态和优先级

{format_instructions}

示例：
用户："查询用户信息"
输出：
{{
    "reasoning": "用户需要获取用户信息，这是一个简单的查询任务，直接使用查询工具即可完成。",
    "user_goal": "获取用户的详细信息",
    "approach": "直接查询获取信息",
    "initial_tasks": [
        {{
            "id": "task_1",
            "description": "查询用户基本信息",
            "query": "用户信息查询",
            "tool_type": "query_tool",
            "intent": "获取用户数据",
            "status": "not_started",
            "dependencies": [],
            "priority": 1
        }}
    ],
    "need_chart": false,
    "execution_strategy": "sequential"
}}"""),
            ("user", "{user_input}")
        ])

        try:
            parser = PydanticOutputParser(pydantic_object=PlanningResult)
            chain = prompt | self.llm | parser
            result = await chain.ainvoke({
                "available_tools": available_tools,
                "user_input": str(message.content),
                "format_instructions": parser.get_format_instructions()
            })
            logger.info("[FourLayerCoordinator] 简单任务规划完成")
            return result.model_dump()
        except Exception as e:
            logger.warning(f"简单任务规划失败，使用默认规划: {e}")
            return self._get_default_simple_plan(message)

    async def _plan_complex_task(self, message: AnyMessage) -> Dict[str, Any]:
        """复杂任务规划 - 执行完整的四层架构流程"""

        # 第二层：工作级规划
        logger.info("[FourLayerCoordinator] 开始工作级规划...")
        work_plan = await self.work_planner.plan(message)

        # 第三层：任务级规划
        logger.info("[FourLayerCoordinator] 开始任务级规划...")
        available_tools = self.tool_registry.get_available_tools_desc()
        task_plan = await self.task_planner.plan(work_plan, available_tools)

        # 合并工作级和任务级规划结果
        result = {
            **task_plan,
            "work_plan": work_plan,  # 保存工作级规划信息
            "planning_layers": {
                "work_level": work_plan,
                "task_level": task_plan
            }
        }

        logger.info(
            f"[FourLayerCoordinator] 复杂任务规划完成，工作阶段数: {len(work_plan.get('work_phases', []))}, 任务数: {len(task_plan.get('initial_tasks', []))}")
        return result

    def _get_default_simple_plan(self, message: AnyMessage) -> Dict[str, Any]:
        """默认简单任务规划"""
        return {
            "reasoning": "由于简单任务规划失败，采用默认的直接查询方式",
            "user_goal": str(message.content),
            "approach": "直接查询获取信息",
            "initial_tasks": [{
                "id": "task_1",
                "description": str(message.content),
                "query": str(message.content),
                "tool_type": "query_tool",
                "intent": "获取信息",
                "status": "not_started",
                "dependencies": [],
                "priority": 1
            }],
            "need_chart": False,
            "execution_strategy": "sequential"
        }


# 迭代决策器
class IterationDecider:
    """根据当前结果决定是否需要更多任务"""

    def __init__(self, llm):
        self.llm = llm
        self.parser = PydanticOutputParser(pydantic_object=IterationDecision)

        self.prompt = ChatPromptTemplate.from_messages([
            ("system", """你是一个智能助手，负责根据当前的查询结果决定是否需要更多任务来完成用户目标。

# 角色定义
你是一个智能助手，名叫Genie，专门负责迭代决策。

# 工作流程
## 先思考 (Reasoning)
- 目标对比：当前结果是否已经满足用户的原始目标？
- 信息完整性：还缺少哪些关键信息？
- 价值评估：继续查询是否会带来更多价值？
- 质量判断：当前信息质量是否足够支撑最终回答？
- 建议控制"思考过程 Reasoning"内容在 200 字以内

## 然后决策 (Decision)
你需要分析：
1. **用户的原始目标**：{user_goal}
2. **原定解决思路**：{approach}
3. **当前已有结果**：{current_results}
4. **是否需要补充**：还需要什么信息？
5. **是否需要生成图表**: 当前任务是否需要进行图表生成？

{format_instructions}

## 决策原则

**不需要更多任务的情况：**
- 已有信息足够回答用户问题
- 用户问题比较简单直接
- 继续查询不会带来更多价值
- 已达到最大迭代次数

**需要更多任务的情况：**
- 缺少关键信息无法完整回答
- 需要进一步的计算或分析
- 发现了新的有价值的分析角度
- 需要多维度查询来获得全面信息

## 新任务设计原则
如果需要新任务，请遵循以下原则：
- 任务描述具体明确
- 查询词包含多个维度的关键词
- 设置合理的依赖关系和优先级
- 每个任务都有明确的intent说明

## 示例

当前结果显示今年销售1000万，去年800万，但用户想要"深度分析"：
{{
    "reasoning": "当前有了基础的销售数据对比，显示25%的增长。但用户要求深度分析，还缺少行业对比基准来判断这个增长率是否优秀，以及缺少增长原因分析。需要补充行业数据和市场环境信息。",
    "need_more_tasks": true,
    "new_tasks": [
        {{
            "id": "task_3",
            "description": "查询行业平均增长率和市场环境",
            "query": "行业平均增长率 市场环境 竞争对手表现",
            "tool_type": "query_tool",
            "intent": "获取对比基准",
            "status": "not_started",
            "dependencies": [],
            "priority": 1
        }}
    ]
}}"""),
            ("placeholder", "{messages}"),
        ])

    async def decide(self, user_goal: str, approach: str, current_results: List[Dict], messages: List[AnyMessage],
                     need_chart: bool) -> \
            Dict[str, Any]:
        """决定是否需要更多任务"""
        try:
            results_summary = "\n".join([f"{r.get('description', '')}: {r.get('result', '')}" for r in current_results])

            chain = self.prompt | self.llm | self.parser
            result = await chain.ainvoke({
                "messages": messages,
                "user_goal": user_goal,
                "approach": approach,
                "current_results": results_summary,
                "format_instructions": self.parser.get_format_instructions(),
                "need_chart": need_chart
            })
            logger.info(f"[IterationDecider] 决策完成: {result.model_dump()}")
            return result.model_dump()
        except Exception as e:
            # 降级：不再增加任务
            logger.warning(f"迭代决策失败，停止迭代: {e}")
            return {
                "reasoning": "由于决策过程出现异常，基于当前信息已足够回答用户问题的判断，停止继续迭代",
                "need_more_tasks": False,
                "new_tasks": []
            }


# 通用结果合并器
class GenericResultMerger:
    """通用结果合并器，内置通用提示词"""

    def __init__(self, llm):
        self.llm = llm

        self.prompt = ChatPromptTemplate.from_messages([
            ("system", """你是专业的智能助手，基于查询结果为用户提供准确、清晰的回答。

你的任务是基于查询结果，直接回答用户的问题。注意：
1. 用专业但友好的语言回答
2. 直接回应用户关心的核心问题
3. 如果是对比查询，重点分析差异和趋势
4. 如果是统计查询，提供关键数据并解读含义
5. 结构清晰，先总结后详述
6. 避免重复信息，突出最有价值的内容
7. 如果用户需要生成图表，直接忽视，因为这个任务会在其他流程中完成，不需要进行任何说明解释，也不要在最后以（注）的形式进行解释

回答风格：
- 开头直接回应用户问题："根据查询结果..."
- 用数据说话，重点解释结果反映的现象
- 语言自然流畅，避免生硬的技术术语
- 如果发现重要信息，主动指出

重要限制：
- 只基于查询结果回答，不要提出超出数据范围的建议
- 专注于解读现有数据

## 查询结果：
{results}"""),
            ("placeholder", "{messages}")
        ])

    async def merge(self, task_results: List[Dict[str, Any]], messages: List[AnyMessage]) -> str:
        """基于查询结果生成用户友好的回答"""
        try:
            # 整理结果
            results_text = ""
            for i, task_result in enumerate(task_results, 1):
                task_desc = task_result.get('description', f'查询{i}')
                task_content = task_result.get('result', '')
                results_text += f"{task_desc}：\n{task_content}\n\n"

            chain = self.prompt | self.llm
            response = await chain.ainvoke({
                "messages": messages,
                "results": results_text.strip()
            })

            return response.content

        except Exception as e:
            # 降级处理：简单拼接结果
            if len(task_results) == 1:
                return f"根据查询结果：\n{task_results[0].get('result', '')}"
            else:
                results = []
                for i, r in enumerate(task_results, 1):
                    results.append(f"结果{i}：{r.get('result', '')}")
                return "\n\n".join(results)


# 通用查询执行器
class GenericQueryExecutor:
    """通用查询执行器，向量库执行"""

    async def execute(self, task: Dict[str, Any], context: Dict[str, Any] = None) -> str:
        """执行查询任务"""
        query = task.get("query", "")
        description = task.get("description", "")

        # 调用向量库查询函数
        result_dict = await execute_multi_query_rerank_search(
            query
        )

        # 将字典结果转换为字符串格式
        if isinstance(result_dict, dict):
            search_summary = result_dict.get("search_summary", "")
            formatted_results = result_dict.get("formatted_results", [])

            # 将结构化数据转换为字符串格式
            result_strings = []
            for case in formatted_results:
                if isinstance(case, dict):
                    case_str = f"""案例{case.get('case_number', 'N/A')}：
                            - 案例标题: {case.get('title', 'N/A')}
                            - 公司名称: {case.get('company_name', 'N/A')} ({case.get('company_code', 'N/A')})
                            - 处罚机构: {case.get('punish_org', 'N/A')}
                            - 违规事项: {case.get('case_list', 'N/A')}
                            - 事件时间: {case.get('event_time', 'N/A')}
                            - Rerank得分: {case.get('relevance_score', 0)}"""
                    if case.get('content_preview'):
                        case_str += f"\n- 相关内容: {case.get('content_preview')}"
                    result_strings.append(case_str)
                else:
                    # 兼容旧的字符串格式
                    result_strings.append(str(case))

            result = search_summary + "\n\n" + "\n\n".join(result_strings)
        else:
            result = str(result_dict)

        return result


# 工具注册表
class ToolRegistry:
    """工具注册表"""

    def __init__(self):
        self._tools: Dict[str, Any] = {}
        self._tool_descriptions: Dict[str, str] = {}

    def register(self, tool_type: str, executor: Any, description: str = ""):
        """注册工具执行器"""
        self._tools[tool_type] = executor
        self._tool_descriptions[tool_type] = description

    def get(self, tool_type: str) -> Any:
        """获取工具执行器"""
        if tool_type not in self._tools:
            raise ValueError(f"未注册的工具类型: {tool_type}")
        return self._tools[tool_type]

    def list_tools(self) -> List[str]:
        """列出所有工具类型"""
        return list(self._tools.keys())

    def get_available_tools_desc(self) -> str:
        """获取可用工具描述，用于提示词"""
        if not self._tool_descriptions:
            return "- query_tool: 通用查询工具（默认）"

        descriptions = []
        for tool_type, desc in self._tool_descriptions.items():
            if desc:
                descriptions.append(f"- {tool_type}: {desc}")
            else:
                descriptions.append(f"- {tool_type}: 工具")
        return "\n".join(descriptions)


# 第四层：执行引擎层 + 四层架构工作流框架
class GenericWorkflowFramework:
    """四层架构工作流框架

    整合四个层次：
    1. 意图理解与复杂度分析层 (EnhancedSupervisorAgent)
    2. 工作级规划层 (WorkLevelPlanner)
    3. 任务级规划层 (TaskLevelPlanner)
    4. 执行引擎层 (LangGraph工作流)

    特性：
    • 智能分层规划：根据任务复杂度选择规划深度
    • 工具生态支持：可扩展的工具注册和匹配
    • 并行执行优化：支持任务依赖关系和并行处理
    • 迭代优化：边执行边调整策略
    """

    def __init__(self, llm):
        self.llm = llm

        # 初始化工具注册表
        self.tool_registry = ToolRegistry()
        self.tool_registry.register("query_tool", GenericQueryExecutor(), "通用查询工具，支持向量搜索")

        # 初始化四层架构组件
        # 第一层：意图理解与复杂度分析层
        self.supervisor = EnhancedSupervisorAgent(llm, self.tool_registry)
        # 第二、三层协调器：工作级规划层 + 任务级规划层
        self.planner = FourLayerPlanningCoordinator(llm, self.tool_registry)
        self.simple_response_generator = SimpleResponseGenerator(llm)
        self.iteration_decider = IterationDecider(llm)
        self.chart_generator = ChartGenerator(llm)
        self.merger = GenericResultMerger(llm)

        # 构建工作流图（不编译，留给用户自定义编译）
        self.graph = self._build_graph()

    def _build_graph(self) -> StateGraph:
        """构建人类化工作流图"""
        graph = StateGraph(GenericWorkflowState)

        # 添加节点
        graph.add_node("supervisor", self._supervisor_node)  # 1. 理解问题
        graph.add_node("simple_response", self._simple_response_node)
        graph.add_node("planning", self._planning_node)  # 2. 快速规划
        graph.add_node("dispatcher", self._dispatcher_node)  # 3. 并行执行
        graph.add_node("task_worker", self._task_worker_node)
        graph.add_node("iteration_check", self._iteration_check_node)  # 4. 边做边想
        graph.add_node("aggregation", self._aggregation_node)  # 5. 整合回答
        graph.add_node("generate_chart", self._charts_node)  # 6. 生成可视化图表
        graph.add_node("final_merge", self._final_merge_node)  # 新增汇聚节点

        # 设置流程
        graph.add_edge(START, "supervisor")

        # 监督者路由 - 使用类型安全的路由函数
        def supervisor_router(state: GenericWorkflowState) -> Literal["simple_response", "planning"]:
            """类型安全的监督者路由"""
            return "simple_response" if state.get("intent_type") == "simple_chat" else "planning"

        graph.add_conditional_edges(
            "supervisor",
            supervisor_router,
            {
                "simple_response": "simple_response",
                "planning": "planning"
            }
        )

        graph.add_edge("simple_response", END)

        # 规划后执行任务
        graph.add_conditional_edges(
            "planning",
            lambda state: "dispatcher" if state.get("current_tasks") else "aggregation",
            {
                "dispatcher": "dispatcher",
                "aggregation": "aggregation"
            }
        )

        # 任务执行和迭代
        graph.add_conditional_edges("dispatcher", self._dispatcher_router)
        graph.add_edge("task_worker", "iteration_check")

        # 迭代决策 - iteration_check 节点使用条件边处理动态路由
        graph.add_conditional_edges("iteration_check", self._iteration_check_router)

        graph.add_edge("aggregation", "final_merge")
        graph.add_edge("generate_chart", "final_merge")
        graph.add_edge("final_merge", END)

        return graph

    async def _supervisor_node(self, state: GenericWorkflowState) -> Dict[str, Any]:
        """监督者节点 - 理解问题并分类意图"""
        # 发送流程信息
        try:
            writer = get_stream_writer()
            writer(json.dumps({"AI_AGENT_FLOW": "🤔 算算正在理解您的需求..."}, ensure_ascii=False))
        except Exception as e:
            logger.warning(f"发送流程信息失败: {e}")

        messages = state.get("messages", [])

        # 使用增强的意图分析
        result = await self.supervisor.analyze_intent_and_complexity(messages)
        return {
            "intent_type": result.get("intent_type"),
            "complexity_level": result.get("intent_type"),  # 复用intent_type作为复杂度
            "task_results": []
        }

    async def _simple_response_node(self, state: GenericWorkflowState) -> Dict[str, Any]:
        """简单回复节点 - 处理简单对话"""
        messages = state.get("messages", [])

        # 获取用户最后一条消息
        user_input = ""
        if messages:
            user_input = str(messages[-1].content)

        # 使用LLM生成智能回复
        response = await self.simple_response_generator.generate_response(user_input)

        return {"messages": [AIMessage(content=response)]}

    async def _planning_node(self, state: GenericWorkflowState) -> Dict[str, Any]:
        """规划节点 - 分层规划或快速规划"""
        # 发送流程信息
        try:
            writer = get_stream_writer()
            complexity_level = state.get("complexity_level", "simple_task")
            if complexity_level == "simple_task":
                writer(json.dumps({"AI_AGENT_FLOW": "🧠 算算正在进行快速任务规划..."}, ensure_ascii=False))
            else:
                writer(json.dumps({"AI_AGENT_FLOW": "🧠 算算正在进行四层架构深度规划..."}, ensure_ascii=False))
        except Exception as e:
            logger.warning(f"发送流程信息失败: {e}")

        messages = state.get("messages", [])

        if not messages:
            return {"current_tasks": [], "task_results": []}

        last_message = messages[-1]

        # 使用增强的分层规划
        intent_result = {"intent_type": state.get("complexity_level", "simple_task")}
        result = await self.planner.plan(last_message, intent_result)

        # 发送任务数量信息
        try:
            task_count = len(result.get("initial_tasks", []))
            writer = get_stream_writer()
            writer(json.dumps({"AI_AGENT_FLOW": f"🚀 启动{task_count}个智能搜索引擎，全面检索中..."}, ensure_ascii=False))
        except Exception as e:
            logger.warning(f"发送流程信息失败: {e}")

        return {
            "user_goal": result.get("user_goal"),
            "approach": result.get("approach"),
            "current_tasks": result.get("initial_tasks", []),
            "task_results": [],
            "iteration_count": 0,
            "need_chart": result.get("need_chart", False),
            "work_plan": result.get("work_plan")  # 保存工作级规划结果
        }

    def _dispatcher_node(self, state: GenericWorkflowState) -> Dict[str, Any]:
        """任务分发节点 - 并行执行能同时做的任务"""
        current_tasks = state.get("current_tasks", [])
        return {} if current_tasks else {"task_results": []}

    def _dispatcher_router(self, state: GenericWorkflowState):
        """任务分发路由 - 并行执行当前任务"""
        current_tasks = state.get("current_tasks", [])
        if not current_tasks:
            return []

        return [
            Send("task_worker", {
                "task": task,
                "context": state.get("context"),
                "task_results": []
            })
            for task in current_tasks
        ]

    async def _task_worker_node(self, state: TaskState) -> Dict[str, Any]:
        """任务工作节点 - 执行具体任务"""
        task = state.get("task", {})
        context = state.get("context", {})

        try:
            tool_type = task.get('tool_type', 'query_tool')
            executor = self.tool_registry.get(tool_type)

            result = await executor.execute(task, context)

            task_result = {
                "task_id": task["id"],
                "description": task["description"],
                "tool_type": tool_type,
                "result": result,
                "status": "completed"
            }

            return {"task_results": [task_result]}

        except Exception as e:
            task_result = {
                "task_id": task.get("id", "unknown"),
                "description": task.get("description", "未知任务"),
                "tool_type": task.get('tool_type', 'query_tool'),
                "result": f"执行失败: {str(e)}",
                "status": "failed"
            }

            return {"task_results": [task_result]}

    def _iteration_check_router(self, state: GenericWorkflowState):
        """迭代检查路由器 - 根据状态决定下一步"""
        # 检查是否需要更多任务
        if state.get("need_more_tasks", False):
            new_tasks = state.get("new_iteration_tasks", [])
            if new_tasks:
                return [
                    Send("dispatcher", {
                        **state,
                        "current_tasks": new_tasks,
                        "iteration_count": state.get("iteration_count", 0) + 1,
                        "need_more_tasks": False,
                        "new_iteration_tasks": []
                    })
                ]

        # 由AI自主判断是否需要生成图表
        need_chart = state.get("need_chart", False)
        # 判断任务数是否小于2，如果小于2则不进行图表生成
        current_tasks = state.get("current_tasks", [])

        if len(current_tasks) < 2:
            need_chart = False
            # 如果用户明确说明需要图表
            user_query = state.get("messages", [])[0].content.lower() if (
                    state.get("messages") and len(state.get("messages")) > 0) else ""
            if any(keyword in user_query for keyword in ["图表", "可视化", "图形", "chart", "graph"]):
                need_chart = True

        if need_chart:
            # 并行发送到两个节点
            return [
                Send("aggregation", state),
                Send("generate_chart", state)
            ]
        else:
            return [Send("aggregation", state)]

    async def _iteration_check_node(self, state: GenericWorkflowState) -> Dict[str, Any]:
        """迭代检查节点 - 边做边想，根据结果调整思路"""
        # 发送流程信息
        try:
            writer = get_stream_writer()
            writer(json.dumps({"AI_AGENT_FLOW": "📊 算算正在深度分析搜索结果..."}, ensure_ascii=False))
        except Exception as e:
            logger.warning(f"发送流程信息失败: {e}")

        task_results = state.get("task_results", [])
        iteration_count = state.get("iteration_count", 0)
        max_iterations = state.get("max_iterations", 5)
        user_goal = state.get("user_goal", "")
        approach = state.get("approach", "")
        messages = state.get("messages", [])

        # 检查是否达到最大迭代次数
        if iteration_count >= max_iterations:
            try:
                writer = get_stream_writer()
                writer(json.dumps({"AI_AGENT_FLOW": "📝 搜索完成，算算正在整理专业报告..."}, ensure_ascii=False))
            except Exception as e:
                logger.warning(f"发送流程信息失败: {e}")
            return {
                "need_more_tasks": False,
                "new_iteration_tasks": []
            }

        # 如果没有结果，停止迭代
        if not task_results:
            try:
                writer = get_stream_writer()
                writer(json.dumps({"AI_AGENT_FLOW": "📝 搜索完成，算算正在整理专业报告..."}, ensure_ascii=False))
            except Exception as e:
                logger.warning(f"发送流程信息失败: {e}")
            return {
                "need_more_tasks": False,
                "new_iteration_tasks": []
            }

        # 使用迭代决策器判断是否需要更多任务
        decision = await self.iteration_decider.decide(user_goal, approach, task_results, messages, need_chart=False)

        if decision.get("need_more_tasks", False):
            new_tasks = decision.get("new_tasks", [])
            try:
                writer = get_stream_writer()
                writer(json.dumps({"AI_AGENT_FLOW": f"🔄 算算发现需要更多信息，启动{len(new_tasks)}个补充搜索..."},
                                  ensure_ascii=False))
            except Exception as e:
                logger.warning(f"发送流程信息失败: {e}")
            return {
                "need_more_tasks": True,
                "new_iteration_tasks": new_tasks
            }
        else:
            try:
                writer = get_stream_writer()
                writer(json.dumps({"AI_AGENT_FLOW": "📝 搜索完成，算算正在整理专业报告..."}, ensure_ascii=False))
            except Exception as e:
                logger.warning(f"发送流程信息失败: {e}")

            return {
                "need_more_tasks": False,
                "new_iteration_tasks": []
            }

    async def _aggregation_node(self, state: GenericWorkflowState) -> Dict[str, Any]:
        """结果汇总节点 - 整合回答"""
        task_results = state.get("task_results", [])
        messages = state.get("messages", [])

        if not task_results:
            error_msg = "抱歉，没有获取到任何查询结果"
            return {
                "messages": [AIMessage(content=error_msg)]
            }

        final_result = await self.merger.merge(task_results, messages)

        return {
            "final_result": None,
            "task_results": [],
            "messages": [AIMessage(content=final_result)]
        }

    async def _charts_node(self, state: GenericWorkflowState) -> Dict[str, Any]:
        # 将汇总结果和数据经过分析，生成指定格式的图表json
        task_results = state.get("task_results", [])
        messages = state.get("messages", [])

        try:
            writer = get_stream_writer()
            writer(json.dumps({"AI_AGENT_FLOW": "📈 汇总完成，算算正在尝试生成可视化图表..."}, ensure_ascii=False))

            # 调用AI生成图表数据
            final_result = await self.chart_generator.generate_response(task_results, messages)

            # 将AI生成结果格式化为AI_AGENT_CHART类型
            writer(json.dumps({
                "AI_AGENT_CHART": final_result
            }, ensure_ascii=False))
        except Exception as e:
            logger.warning(f"发送流程信息失败: {e}")
            final_result = str(e)

        return {
            "task_results": [],
            "current_tasks": []
        }

    async def _final_merge_node(self, state: GenericWorkflowState) -> Dict[str, Any]:
        """最终汇聚节点 - 等待并行任务完成"""
        # 这个节点主要是等待两个并行任务都完成
        # 可以在这里做一些最终的处理，比如合并结果

        # 如果需要，可以将图表结果和文本结果进行最终整合
        messages = state.get("messages", [])

        return {
            "messages": messages  # 保持现有的消息
        }

    def compile(self, **kwargs):
        """编译工作流图，支持自定义配置（如 checkpointer）"""
        return self.graph.compile(**kwargs)

    def get_initial_state(self, message: str, **kwargs) -> Dict[str, Any]:
        """获取初始状态模板，用户可以基于此自定义"""
        return {
            "messages": [("user", message)],
            "context": kwargs.get("context", {}),
            "intent_type": None,
            "user_goal": None,
            "approach": None,
            "current_tasks": None,
            "task_results": [],
            "iteration_count": 0,
            "max_iterations": kwargs.get("max_iterations", 5),
            "need_more_tasks": False,
            "new_iteration_tasks": [],
            "final_result": None,
            # 支持用户添加自定义字段
            **{k: v for k, v in kwargs.items() if k not in ["context", "max_iterations"]}
        }

    async def run(self, message: str, context: Dict[str, Any] = None, **kwargs) -> str:
        """简单的运行方法"""
        compiled_graph = self.compile()
        initial_state = self.get_initial_state(message, context=context, **kwargs)

        result = await compiled_graph.ainvoke(initial_state)
        return result["messages"][-1].content


# 便捷函数
def create_generic_workflow(llm=None):
    """创建增强版工作流实例"""
    if llm is None:
        llm = get_a_llm(llm_type='hs-deepseek-v3-0324', temperature=0)

    return GenericWorkflowFramework(llm)


# 示例使用
async def example_usage():
    """示例用法"""
    # 创建工作流
    workflow = create_generic_workflow()

    print("=== 增强版工作流示例 ===")
    # 运行示例
    result1 = await workflow.run("你好")
    print("简单对话示例:", result1)

    result2 = await workflow.run("查询用户数据")
    print("简单任务示例:", result2)

    result3 = await workflow.run("分析过去三年的违规案例趋势并生成详细报告")
    print("复杂任务示例:", result3)


# 工具注册示例
async def tool_registration_example():
    """展示如何注册自定义工具"""
    # 创建工作流
    workflow = create_generic_workflow()

    # 示例：注册一个简单的计算工具
    class SimpleCalculator:
        async def execute(self, task: Dict[str, Any], context: Dict[str, Any] = None) -> str:
            query = task.get("query", "")
            # 简单的计算逻辑
            if "+" in query:
                parts = query.split("+")
                if len(parts) == 2:
                    try:
                        result = float(parts[0].strip()) + float(parts[1].strip())
                        return f"计算结果：{result}"
                    except:
                        pass
            return f"无法计算：{query}"

    # 注册工具
    workflow.tool_registry.register(
        "calculator_tool",
        SimpleCalculator(),
        "简单计算工具，支持加法运算"
    )

    # 测试新工具
    result = await workflow.run("计算 10 + 20")
    print("自定义工具示例:", result)


# 持久化使用示例
async def persistence_example():
    """展示如何使用持久化"""
    # 创建工作流
    workflow = create_generic_workflow()

    # 自定义初始状态
    initial_state = workflow.get_initial_state(
        message="查询用户数据",
        login_company="test_company",  # 自定义字段
        max_iterations=5  # 自定义最大迭代次数
    )

    # 自定义配置
    config = {
        "recursion_limit": 20,
        "configurable": {
            "thread_id": "112233445511223344"  # 持久化线程ID
        }
    }

    # 方式1：不使用持久化（简单编译）
    simple_graph = workflow.compile()
    result = await simple_graph.ainvoke(initial_state, config)
    print("无持久化结果:", result["messages"][-1].content)

    # 方式2：使用持久化（需要你的 checkpointer）
    # async with MyAIOMySQLSaver.from_conn_string(agent_checkpoint_db_uri) as checkpointer:
    #     persistent_graph = workflow.compile(checkpointer=checkpointer)
    #     result = await persistent_graph.ainvoke(initial_state, config, stream_mode="values", debug=True)
    #     print("持久化结果:", result["messages"][-1].content)


if __name__ == "__main__":
    import asyncio

    print("=== 四层架构多Agent工作流框架 ===")
    print("架构层次：")
    print("第一层：意图理解与复杂度分析层 - 智能识别任务复杂度")
    print("第二层：工作级规划层 - 目标分解、策略制定、资源需求分析")
    print("第三层：任务级规划层 - 具体任务生成、工具选择与匹配、依赖关系构建")
    print("第四层：执行引擎层 - 并行任务执行、迭代优化、结果合并")
    print()
    print("智能特性：")
    print("- 简单任务：跳过工作级规划，直接生成任务")
    print("- 复杂任务：完整四层架构，深度规划分解")
    print("- 工具生态：可扩展的工具注册和智能匹配")
    print()

    # 运行示例
    asyncio.run(example_usage())

    print("\n=== 工具注册示例 ===")
    asyncio.run(tool_registration_example())

    print("\n=== 持久化使用示例 ===")
    asyncio.run(persistence_example())
